import React, { useMemo } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import remarkBreaks from "remark-breaks";
import rehypeKatex from "rehype-katex";
import rehypeHighlight from "rehype-highlight";
import { Spin } from "antd";
import "katex/dist/katex.min.css";
import "highlight.js/styles/default.css";

interface MarkdownProps {
  content: string;
  loading?: boolean;
  fontSize?: number;
  fontFamily?: string;
  className?: string;
}

export const Markdown: React.FC<MarkdownProps> = ({
  content,
  loading = false,
  fontSize = 14,
  fontFamily = "inherit",
  className = "",
}) => {
  const markdownContent = useMemo(() => {
    if (loading && !content) {
      return "正在思考中...";
    }
    return content || "";
  }, [content, loading]);

  const components = useMemo(
    () => ({
      // 自定义代码块渲染
      code: ({ node, inline, className, children, ...props }: any) => {
        const match = /language-(\w+)/.exec(className || "");
        const language = match ? match[1] : "";

        if (inline) {
          return (
            <code
              className={`inline-code ${className || ""}`}
              style={{
                backgroundColor: "rgba(0,0,0,0.08)",
                padding: "2px 6px",
                borderRadius: "4px",
                fontSize: fontSize * 0.9,
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                color: "#d63384",
              }}
              {...props}
            >
              {children}
            </code>
          );
        }

        return (
          <div
            className="code-block-wrapper"
            style={{ margin: "8px 0", minWidth: "500px" }}
          >
            {language && (
              <div
                className="code-block-header"
                style={{
                  backgroundColor: "rgba(0,0,0,0.05)",
                  padding: "6px 12px",
                  borderTopLeftRadius: "6px",
                  borderTopRightRadius: "6px",
                  fontSize: fontSize * 0.75,
                  color: "#666",
                  borderBottom: "1px solid rgba(0,0,0,0.1)",
                  fontWeight: "bold",
                }}
              >
                {language}
              </div>
            )}
            <pre
              style={{
                margin: 0,
                backgroundColor: "rgba(0,0,0,0.05)",
                padding: "12px",
                borderRadius: language ? "0 0 6px 6px" : "6px",
                overflow: "auto",
                fontSize: fontSize * 0.85,
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                lineHeight: 1.4,
              }}
            >
              <code className={className} {...props}>
                {children}
              </code>
            </pre>
          </div>
        );
      },

      // 自定义表格渲染
      table: ({ children, ...props }: any) => (
        <div style={{ overflow: "auto", margin: "16px 0" }}>
          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              fontSize: fontSize,
              fontFamily,
            }}
            {...props}
          >
            {children}
          </table>
        </div>
      ),

      th: ({ children, ...props }: any) => (
        <th
          style={{
            border: "1px solid #ddd",
            padding: "8px 12px",
            backgroundColor: "#f5f5f5",
            textAlign: "left",
            fontWeight: "bold",
          }}
          {...props}
        >
          {children}
        </th>
      ),

      td: ({ children, ...props }: any) => (
        <td
          style={{
            border: "1px solid #ddd",
            padding: "8px 12px",
          }}
          {...props}
        >
          {children}
        </td>
      ),

      // 自定义链接渲染
      a: ({ children, href, ...props }: any) => (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          style={{
            color: "#1890ff",
            textDecoration: "none",
          }}
          {...props}
        >
          {children}
        </a>
      ),

      // 自定义引用块渲染
      blockquote: ({ children, ...props }: any) => (
        <blockquote
          style={{
            borderLeft: "4px solid #ddd",
            margin: "16px 0",
            paddingLeft: "16px",
            color: "#666",
            fontStyle: "italic",
          }}
          {...props}
        >
          {children}
        </blockquote>
      ),

      // 自定义列表渲染
      ul: ({ children, ...props }: any) => (
        <ul
          style={{
            paddingLeft: "20px",
            margin: "8px 0",
          }}
          {...props}
        >
          {children}
        </ul>
      ),

      ol: ({ children, ...props }: any) => (
        <ol
          style={{
            paddingLeft: "20px",
            margin: "8px 0",
          }}
          {...props}
        >
          {children}
        </ol>
      ),

      // 自定义段落渲染
      p: ({ children, ...props }: any) => (
        <p
          style={{
            margin: "4px 0",
            lineHeight: 1.5,
            fontSize,
            fontFamily,
          }}
          {...props}
        >
          {children}
        </p>
      ),

      // 自定义标题渲染
      h1: ({ children, ...props }: any) => (
        <h1
          style={{
            fontSize: fontSize * 1.8,
            fontWeight: "bold",
            margin: "20px 0 16px 0",
            borderBottom: "2px solid #eee",
            paddingBottom: "8px",
          }}
          {...props}
        >
          {children}
        </h1>
      ),

      h2: ({ children, ...props }: any) => (
        <h2
          style={{
            fontSize: fontSize * 1.5,
            fontWeight: "bold",
            margin: "18px 0 14px 0",
          }}
          {...props}
        >
          {children}
        </h2>
      ),

      h3: ({ children, ...props }: any) => (
        <h3
          style={{
            fontSize: fontSize * 1.3,
            fontWeight: "bold",
            margin: "16px 0 12px 0",
          }}
          {...props}
        >
          {children}
        </h3>
      ),
    }),
    [fontSize, fontFamily]
  );

  if (loading && !content) {
    return (
      <div style={{ textAlign: "center", padding: "20px" }}>
        <Spin size="small" />
        <span style={{ marginLeft: "8px", fontSize, color: "#666" }}>
          正在生成回复...
        </span>
      </div>
    );
  }

  return (
    <div
      className={`markdown-content ${className}`}
      style={{
        fontSize,
        fontFamily,
        lineHeight: 1.6,
        color: "#333",
      }}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath, remarkBreaks]}
        rehypePlugins={[rehypeKatex, rehypeHighlight]}
        components={components}
      >
        {markdownContent}
      </ReactMarkdown>
    </div>
  );
};
