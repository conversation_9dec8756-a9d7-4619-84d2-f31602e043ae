import { observer } from "mobx-react-lite";
import { useStore } from "../../store";
import { Navigate, useLocation } from "react-router-dom";
import React from "react";
import { CoRouteObject } from "@/types/route";
import { authService } from "../../services/auth";
import { message } from "antd";
import { toJS } from "mobx";
import routes from "../../router";

const AuthBoundary: React.FC<React.PropsWithChildren> = observer((props) => {
  const { children } = props;
  const { UserStore } = useStore();
  const location = useLocation();

  // 检查用户是否登录和token是否有效
  const isAuthenticated = authService.isAuthenticated();

  if (!isAuthenticated) {
    // 未登录、无用户信息或token无效，跳转到登录页
    // message.error('登录失效，请先登录');
    return (
      <Navigate to="/auth/login" state={{ from: location }} replace={true} />
    );
  }

  // 查找当前路径对应的路由配置
  const findRouteByPath = (
    routes: CoRouteObject[],
    path: string
  ): CoRouteObject | undefined => {
    for (const route of routes) {
      if (route.path === path) return route;
      if (route.children) {
        const found = findRouteByPath(route.children, path);
        if (found) return found;
      }
    }
    return undefined;
  };

  // 检查路由权限
  const hasRoutePermission = (route: CoRouteObject): boolean => {
    // 跳过layout=false的路由（如登录页、错误页等）
    if (route.meta?.layout === false) {
      return true;
    }

    // 跳过根路由
    if (route.root) {
      return true;
    }

    // 1. 检查角色权限
    if (route.meta?.roles?.length) {
      if (!UserStore.hasAnyRole(route.meta.roles)) {
        return false;
      }
    }

    // 2. 检查动态路由权限
    if (route.path) {
      const dynamicRoutes = UserStore.userInfo?.dynamicRoutesList || [];
      const needCheckDynamicRoute = route.meta?.layout !== false;
      const hasPermission =
        !needCheckDynamicRoute ||
        dynamicRoutes.includes("/") ||
        dynamicRoutes.includes(route.path);
      if (!hasPermission) {
        return false;
      }
    }

    return true;
  };

  // 获取当前路由配置（从原始路由配置中查找，而不是过滤后的）
  const currentRoute = findRouteByPath(routes, location.pathname);

  // 如果找到路由配置且有重定向属性，则进行重定向
  if (currentRoute?.redirect) {
    return <Navigate to={currentRoute.redirect} replace={true} />;
  }

  // 检查当前路由权限
  if (currentRoute && !hasRoutePermission(currentRoute)) {
    // 无权限访问，重定向到403页面
    return <Navigate to="/error/403" replace={true} />;
  }

  // 用户已登录且有权限，渲染子组件
  return <>{children}</>;
});

export default AuthBoundary;
