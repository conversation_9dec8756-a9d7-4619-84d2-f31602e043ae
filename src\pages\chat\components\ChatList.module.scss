/* ChatList 组件样式 */

.chatListContainer {
  height: 100%;
  background: #f7f7fc;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .headerTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .collapseButton {
      padding: 4px;
      border-radius: 4px;

      &:hover {
        background: #f0f0f0;
      }
    }

    .searchContainer {
      flex: 1;
      display: flex;
      justify-content: flex-end;

      .searchButton {
        padding: 4px;
        border-radius: 4px;

        &:hover {
          background: #f0f0f0;
        }
      }
    }
  }

  .buttonGroup {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.newChatButton {
  background-color: #ffffff;

  color: #4318ff;
  &:hover {
    background-color: #eeeeee !important;
    border-color: #f8f8f8 !important;
    color: #4318ff !important;
  }
}

.divider {
  margin: 12px 0;
}

.navigationButtons {
  width: 100%;
}

.navigationButton {
  text-align: left;
  justify-content: flex-start;

  &.active {
    background-color: #7b4ffe;
    border-color: #7b4ffe;
    color: #fff;
  }

  &.inactive {
    background-color: transparent;
    border-color: #d9d9d9;
    color: #666;
  }
}

.searchInput {
  // 使用默认样式
}

.chatListContent {
  flex: 1;
  overflow: auto;
  padding: 16px 16px 20px 16px; /* 增加底部 padding 确保最后一项边框完全显示 */

  .emptyList {
    margin-top: 60px;
  }

  .sessionGroups {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .agentGroup {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .agentHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s;

      &:hover {
        background: #f0f0f0;
      }

      .agentInfo {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        flex: 1;

        .agentName {
          font-size: 14px;
          color: #333;
        }

        .collapseIcon {
          font-size: 12px;
          color: #666;
          transition: transform 0.2s;
          margin-left: auto;

          &.expanded {
            transform: rotate(90deg);
          }

          &.collapsed {
            transform: rotate(0deg);
          }
        }
      }

      .addSessionButton {
        padding: 4px 8px;
        border-radius: 4px;
        color: #603cff;
        &:hover {
          // background: #f6ffed;
          color: #603cff;
        }
      }
    }

    .agentSessions {
      padding: 8px;
    }
  }

  .normalSessions {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .sectionHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }
  }

  .sessionItem {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;

    &:hover {
      background: #f8f8f8;
      border-radius: 15px;
    }

    &.active {
      background: #f6f2ff;
      border-radius: 15px;
    }

    .sessionContent {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      position: relative;

      .messageIcon {
        font-size: 16px;
        color: #666;
        margin-top: 2px;
      }

      .sessionInfo {
        flex: 1;
        min-width: 0;

        .titleRow {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 4px;
          gap: 8px;

          .titleContent {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
            max-width: calc(100% - 60px); // 为操作图标预留空间

            .agentIcon {
              flex-shrink: 0;
              transition: color 0.2s;
            }
          }

          .sessionTitle {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
            min-width: 0;

            &.active {
              color: #6f3bfe;
              font-weight: 600;
            }

            &.inactive {
              color: #333;
              font-weight: 400;
            }
          }

          .moreIcon {
            opacity: 1; // 直接显示，通过JavaScript控制显示/隐藏
            transition: all 0.2s ease;
            border: none !important;
            box-shadow: none !important;
            padding: 4px !important;
            width: 20px !important;
            height: 20px !important;
            min-width: 20px !important;

            &:hover {
              background: #f6f2ff !important;
            }

            &:focus {
              background: #f6f2ff !important;
            }

            .anticon {
              font-size: 14px;
              color: #666;
            }

            &:hover .anticon {
              color: #333;
            }
          }
        }
      }
    }

    // 移除CSS悬停，使用JavaScript控制显示
  }
}

/* 已清理不再使用的样式 */

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px;
  }

  .chatListItem {
    padding: 10px 12px;
  }
}
